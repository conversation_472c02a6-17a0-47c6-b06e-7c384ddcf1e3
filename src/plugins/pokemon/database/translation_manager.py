"""
Translation Manager

Handles translation between Chinese and English names for Pokemon, moves, items, abilities, etc.
"""

import json
import logging
import re
from typing import Dict, Optional, Set, List
from pathlib import Path

logger = logging.getLogger(__name__)


class TranslationManager:
    """Manages translations between Chinese and English names"""
    
    def __init__(self):
        self._translations: Dict[str, str] = {}  # Chinese -> English
        self._reverse_translations: Dict[str, str] = {}  # English -> Chinese
        self._initialized = False
        
    async def initialize(self) -> bool:
        """Initialize translation data"""
        if self._initialized:
            return True
            
        try:
            # Load translations from the JavaScript file
            translations_file = Path("data/pokemon_cache/translations.js")
            if not translations_file.exists():
                logger.warning("Translations file not found")
                return False
                
            await self._load_translations(translations_file)
            self._initialized = True
            logger.info(f"Loaded {len(self._translations)} translations")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing translation manager: {e}")
            return False
    
    async def _load_translations(self, file_path: Path):
        """Load translations from JavaScript file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract the translations object from JavaScript
            # Look for patterns like "English Name": "中文名称"
            pattern = r'"([^"]+)":\s*"([^"]+)"'
            matches = re.findall(pattern, content)
            
            for english, chinese in matches:
                # Skip UI translations and focus on Pokemon-related content
                if self._is_pokemon_related(english, chinese):
                    # Normalize names for better matching
                    english_normalized = self._normalize_name(english)
                    chinese_normalized = self._normalize_name(chinese)
                    
                    # Store both directions
                    self._translations[chinese_normalized] = english_normalized
                    self._reverse_translations[english_normalized] = chinese_normalized
                    
                    # Also store original forms
                    self._translations[chinese] = english
                    self._reverse_translations[english] = chinese
                    
        except Exception as e:
            logger.error(f"Error loading translations from {file_path}: {e}")
    
    def _is_pokemon_related(self, english: str, chinese: str) -> bool:
        """Check if a translation pair is Pokemon-related"""
        # Skip obvious UI translations
        ui_keywords = [
            "Username", "Password", "Cancel", "OK", "Yes", "No", "Login", 
            "Register", "Search", "Loading", "Connecting", "Battle", "Team",
            "Format", "Ladder", "Tournament", "Chat", "Room", "User",
            "Settings", "Options", "Graphics", "Sound", "Music", "Volume"
        ]
        
        # Skip if it's clearly a UI element
        if any(keyword.lower() in english.lower() for keyword in ui_keywords):
            return False
            
        # Include if it contains Pokemon-related patterns
        pokemon_patterns = [
            # Pokemon names (usually capitalized single words or hyphenated)
            r'^[A-Z][a-z]+(-[A-Z][a-z]+)*$',
            # Move names (can be multiple words)
            r'^[A-Z][a-z]+(\s+[A-Z][a-z]+)*$',
            # Item names (often end with specific suffixes)
            r'.*(Berry|Stone|Orb|Plate|Drive|Memory|Z)$',
            # Ability names
            r'^[A-Z][a-z]+(\s+[A-Z][a-z]+)*$'
        ]
        
        # Check if English name matches Pokemon-related patterns
        for pattern in pokemon_patterns:
            if re.match(pattern, english):
                return True
                
        # Include if Chinese contains Pokemon-related characters
        pokemon_chars = ['宝可梦', '精灵', '球', '果', '石', '珠', '板', '卡', '带']
        if any(char in chinese for char in pokemon_chars):
            return True
            
        return False
    
    def _normalize_name(self, name: str) -> str:
        """Normalize name for better matching"""
        # Remove special characters and convert to lowercase
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '', name.lower())
        return normalized
    
    def translate_to_english(self, chinese_name: str) -> Optional[str]:
        """Translate Chinese name to English"""
        if not self._initialized:
            return None
            
        # Try exact match first
        if chinese_name in self._translations:
            return self._translations[chinese_name]
            
        # Try normalized match
        normalized = self._normalize_name(chinese_name)
        if normalized in self._translations:
            return self._translations[normalized]
            
        # Try partial matching for Pokemon names
        for chinese_key, english_value in self._translations.items():
            if chinese_name in chinese_key or chinese_key in chinese_name:
                return english_value
                
        return None
    
    def translate_to_chinese(self, english_name: str) -> Optional[str]:
        """Translate English name to Chinese"""
        if not self._initialized:
            return None
            
        # Try exact match first
        if english_name in self._reverse_translations:
            return self._reverse_translations[english_name]
            
        # Try normalized match
        normalized = self._normalize_name(english_name)
        if normalized in self._reverse_translations:
            return self._reverse_translations[normalized]
            
        return None
    
    def get_all_chinese_names(self) -> Set[str]:
        """Get all available Chinese names"""
        return set(self._translations.keys())
    
    def get_all_english_names(self) -> Set[str]:
        """Get all available English names"""
        return set(self._reverse_translations.keys())
    
    def search_chinese_names(self, query: str, limit: int = 10) -> List[str]:
        """Search for Chinese names containing the query"""
        query_lower = query.lower()
        results = []
        
        for chinese_name in self._translations.keys():
            if query_lower in chinese_name.lower() and len(results) < limit:
                results.append(chinese_name)
                
        return results
    
    def search_english_names(self, query: str, limit: int = 10) -> List[str]:
        """Search for English names containing the query"""
        query_lower = query.lower()
        results = []
        
        for english_name in self._reverse_translations.keys():
            if query_lower in english_name.lower() and len(results) < limit:
                results.append(english_name)
                
        return results


# Global instance
translation_manager = TranslationManager()
